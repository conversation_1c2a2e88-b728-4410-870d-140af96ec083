
import React, { useState, useRef, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Mic, Square, Play, Pause, Settings, Download, FileText, Brain, Volume2, Zap } from 'lucide-react';
import AudioRecorder from '@/components/AudioRecorder';
import ProcessingPipeline from '@/components/ProcessingPipeline';
import LogViewer from '@/components/LogViewer';
import ConfigPanel from '@/components/ConfigPanel';
import SessionHistory from '@/components/SessionHistory';
import STTTester from '@/components/STTTester';
import LLMTester from '@/components/LLMTester';
import TTSTester from '@/components/TTSTester';
import ApiConfigDialog from '@/components/ApiConfigDialog';
import { useToast } from '@/hooks/use-toast';
import ToolsManager, { Tool } from '@/components/ToolsManager';

const Index = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [currentSession, setCurrentSession] = useState(null);
  const [systemPrompt, setSystemPrompt] = useState(
    "You are an AI voice assistant. Respond naturally and concisely to the user's questions. Keep responses under 100 words unless more detail is specifically requested."
  );
  const [selectedVoice, setSelectedVoice] = useState('Aria - 9BWtsMINqrJLrRacOk9x');
  const [processingStage, setProcessingStage] = useState('idle');
  const [sessionLogs, setSessionLogs] = useState([]);
  const [tools, setTools] = useState<Tool[]>([]);
  const { toast } = useToast();

  const handleStartRecording = useCallback(() => {
    setIsRecording(true);
    setProcessingStage('recording');
    const sessionId = `session_${Date.now()}`;
    setCurrentSession(sessionId);
    toast({
      title: "Recording Started",
      description: "Speak into your microphone to test the voice pipeline",
    });
  }, [toast]);

  const handleStopRecording = useCallback((audioBlob) => {
    setIsRecording(false);
    setProcessingStage('processing');
    
    // Simulate processing pipeline
    setTimeout(() => {
      const newLog = {
        id: currentSession,
        timestamp: new Date().toISOString(),
        inputAudio: audioBlob,
        transcript: "This is a sample transcript from Deepgram STT",
        gptResponse: "This is a sample response from GPT-4",
        ttsAudio: null, // Would be generated from ElevenLabs
        status: 'completed'
      };
      
      setSessionLogs(prev => [newLog, ...prev]);
      setProcessingStage('completed');
      
      toast({
        title: "Processing Complete",
        description: "Voice pipeline test completed successfully",
      });
    }, 3000);
  }, [currentSession, toast]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="max-w-7xl mx-auto p-6 space-y-8">
        {/* Modern Header */}
        <div className="text-center space-y-4 py-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
              Voice AI Testing Framework
            </h1>
          </div>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Modular testing sandbox for Speech-to-Text, Language Models, and Text-to-Speech components
          </p>
          
          {/* Quick Actions */}
          <div className="flex justify-center gap-4 pt-4">
            <ApiConfigDialog
              trigger={
                <Button variant="outline" size="sm" className="bg-white/50 backdrop-blur-sm border-white/20">
                  <Settings className="w-4 h-4 mr-2" />
                  API Configuration
                </Button>
              }
            />
            <Button variant="outline" size="sm" className="bg-white/50 backdrop-blur-sm border-white/20">
              <Download className="w-4 h-4 mr-2" />
              Export All Logs
            </Button>
          </div>
        </div>

        {/* Main Tabs */}
        <Tabs defaultValue="full-pipeline" className="w-full">
          <TabsList className="grid w-full grid-cols-6 bg-white/70 backdrop-blur-sm border border-white/20 shadow-lg rounded-xl p-1">
            <TabsTrigger 
              value="full-pipeline" 
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white rounded-lg transition-all duration-200"
            >
              <Zap className="w-4 h-4 mr-2" />
              Full Pipeline
            </TabsTrigger>
            <TabsTrigger 
              value="stt-test"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-indigo-500 data-[state=active]:text-white rounded-lg transition-all duration-200"
            >
              <Mic className="w-4 h-4 mr-2" />
              STT Testing
            </TabsTrigger>
            <TabsTrigger 
              value="llm-test"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-500 data-[state=active]:text-white rounded-lg transition-all duration-200"
            >
              <Brain className="w-4 h-4 mr-2" />
              LLM Testing
            </TabsTrigger>
            <TabsTrigger 
              value="tts-test"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-500 data-[state=active]:text-white rounded-lg transition-all duration-200"
            >
              <Volume2 className="w-4 h-4 mr-2" />
              TTS Testing
            </TabsTrigger>
            <TabsTrigger 
              value="tools"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white rounded-lg transition-all duration-200"
            >
              <Settings className="w-4 h-4 mr-2" />
              AI Tools
            </TabsTrigger>
            <TabsTrigger 
              value="logs"
              className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-gray-500 data-[state=active]:to-slate-500 data-[state=active]:text-white rounded-lg transition-all duration-200"
            >
              <FileText className="w-4 h-4 mr-2" />
              Session Logs
            </TabsTrigger>
          </TabsList>
          
          <div className="mt-8">
            <TabsContent value="full-pipeline" className="space-y-6">
              {/* Full Pipeline Interface */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Recording & Control Panel */}
                <div className="lg:col-span-1 space-y-4">
                  <AudioRecorder
                    isRecording={isRecording}
                    onStartRecording={handleStartRecording}
                    onStopRecording={handleStopRecording}
                    processingStage={processingStage}
                  />
                  
                  <ConfigPanel
                    systemPrompt={systemPrompt}
                    onSystemPromptChange={setSystemPrompt}
                    selectedVoice={selectedVoice}
                    onVoiceChange={setSelectedVoice}
                  />
                </div>

                {/* Processing Pipeline & Results */}
                <div className="lg:col-span-2 space-y-4">
                  <ProcessingPipeline
                    stage={processingStage}
                    currentSession={currentSession}
                  />
                  
                  <Tabs defaultValue="current-logs" className="w-full">
                    <TabsList className="grid w-full grid-cols-2 bg-white/70 backdrop-blur-sm">
                      <TabsTrigger value="current-logs" className="data-[state=active]:bg-white/90">
                        Current Session
                      </TabsTrigger>
                      <TabsTrigger value="history" className="data-[state=active]:bg-white/90">
                        History
                      </TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="current-logs" className="mt-4">
                      <LogViewer
                        sessionLogs={sessionLogs}
                        currentSession={currentSession}
                      />
                    </TabsContent>
                    
                    <TabsContent value="history" className="mt-4">
                      <SessionHistory
                        sessions={sessionLogs}
                        onSessionSelect={(session) => setCurrentSession(session.id)}
                      />
                    </TabsContent>
                  </Tabs>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="stt-test">
              <STTTester />
            </TabsContent>
            
            <TabsContent value="llm-test">
              <LLMTester tools={tools} />
            </TabsContent>
            
            <TabsContent value="tts-test">
              <TTSTester />
            </TabsContent>

            <TabsContent value="tools">
              <ToolsManager tools={tools} onToolsChange={setTools} />
            </TabsContent>
            
            <TabsContent value="logs">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <LogViewer
                  sessionLogs={sessionLogs}
                  currentSession={currentSession}
                />
                <SessionHistory
                  sessions={sessionLogs}
                  onSessionSelect={(session) => setCurrentSession(session.id)}
                />
              </div>
            </TabsContent>
          </div>
        </Tabs>

        {/* Enhanced Status Bar */}
        <Card className="bg-white/70 backdrop-blur-sm border-white/20 shadow-lg">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-8">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-gradient-to-r from-green-400 to-green-500 rounded-full animate-pulse"></div>
                  <span className="font-medium text-gray-700">Deepgram STT</span>
                  <span className="text-sm text-green-600 font-semibold">Ready</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full animate-pulse"></div>
                  <span className="font-medium text-gray-700">OpenAI GPT-4</span>
                  <span className="text-sm text-blue-600 font-semibold">Ready</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-gradient-to-r from-purple-400 to-purple-500 rounded-full animate-pulse"></div>
                  <span className="font-medium text-gray-700">ElevenLabs TTS</span>
                  <span className="text-sm text-purple-600 font-semibold">Ready</span>
                </div>
                {tools.filter(t => t.enabled).length > 0 && (
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-gradient-to-r from-orange-400 to-orange-500 rounded-full animate-pulse"></div>
                    <span className="font-medium text-gray-700">AI Tools</span>
                    <span className="text-sm text-orange-600 font-semibold">{tools.filter(t => t.enabled).length} Active</span>
                  </div>
                )}
              </div>
              <div className="text-gray-500 text-sm">
                <span className="font-medium">Sessions:</span> {sessionLogs.length} | 
                <span className="font-medium ml-2">Last:</span> {sessionLogs[0]?.timestamp ? new Date(sessionLogs[0].timestamp).toLocaleTimeString() : 'None'}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Index;
