
import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Mic, Square, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AudioRecorderProps {
  isRecording: boolean;
  onStartRecording: () => void;
  onStopRecording: (audioBlob: Blob) => void;
  processingStage: string;
}

const AudioRecorder: React.FC<AudioRecorderProps> = ({
  isRecording,
  onStartRecording,
  onStopRecording,
  processingStage
}) => {
  const [audioLevel, setAudioLevel] = useState(0);
  const [duration, setDuration] = useState(0);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isRecording) {
      startRecording();
      startDurationTimer();
    } else {
      stopRecording();
      stopDurationTimer();
    }

    return () => {
      stopDurationTimer();
    };
  }, [isRecording]);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // Set up audio analysis for visual feedback
      audioContextRef.current = new AudioContext();
      analyserRef.current = audioContextRef.current.createAnalyser();
      const source = audioContextRef.current.createMediaStreamSource(stream);
      source.connect(analyserRef.current);
      
      // Start recording
      mediaRecorderRef.current = new MediaRecorder(stream);
      const chunks: Blob[] = [];
      
      mediaRecorderRef.current.ondataavailable = (event) => {
        chunks.push(event.data);
      };
      
      mediaRecorderRef.current.onstop = () => {
        const audioBlob = new Blob(chunks, { type: 'audio/wav' });
        onStopRecording(audioBlob);
        stream.getTracks().forEach(track => track.stop());
      };
      
      mediaRecorderRef.current.start();
      
      // Start audio level monitoring
      monitorAudioLevel();
    } catch (error) {
      console.error('Error starting recording:', error);
    }
  };

  const stopRecording = () => {
    mediaRecorderRef.current?.stop();
    audioContextRef.current?.close();
    setAudioLevel(0);
    setDuration(0);
  };

  const monitorAudioLevel = () => {
    if (!analyserRef.current) return;
    
    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
    
    const updateLevel = () => {
      if (!analyserRef.current || !isRecording) return;
      
      analyserRef.current.getByteFrequencyData(dataArray);
      const average = dataArray.reduce((acc, val) => acc + val, 0) / dataArray.length;
      setAudioLevel(average / 255);
      
      requestAnimationFrame(updateLevel);
    };
    
    updateLevel();
  };

  const startDurationTimer = () => {
    durationIntervalRef.current = setInterval(() => {
      setDuration(prev => prev + 0.1);
    }, 100);
  };

  const stopDurationTimer = () => {
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = (seconds % 60).toFixed(1);
    return `${mins}:${secs.padStart(4, '0')}`;
  };

  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Mic className="w-5 h-5" />
          Audio Input
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Recording Button */}
        <div className="flex justify-center">
          <Button
            size="lg"
            onClick={isRecording ? () => {} : onStartRecording}
            disabled={processingStage === 'processing'}
            className={cn(
              "w-24 h-24 rounded-full transition-all duration-300",
              isRecording 
                ? "bg-red-600 hover:bg-red-700 animate-pulse" 
                : "bg-blue-600 hover:bg-blue-700",
              processingStage === 'processing' && "opacity-50 cursor-not-allowed"
            )}
          >
            {processingStage === 'processing' ? (
              <Loader2 className="w-8 h-8 animate-spin" />
            ) : isRecording ? (
              <Square className="w-8 h-8" />
            ) : (
              <Mic className="w-8 h-8" />
            )}
          </Button>
        </div>

        {/* Status */}
        <div className="text-center space-y-2">
          {isRecording && (
            <>
              <div className="text-red-400 font-semibold">
                Recording... {formatDuration(duration)}
              </div>
              
              {/* Audio Level Visualizer */}
              <div className="w-full bg-gray-700 h-2 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-green-500 to-red-500 transition-all duration-100"
                  style={{ width: `${audioLevel * 100}%` }}
                />
              </div>
            </>
          )}
          
          {processingStage === 'processing' && (
            <div className="text-yellow-400 font-semibold">
              Processing audio through pipeline...
            </div>
          )}
          
          {processingStage === 'completed' && (
            <div className="text-green-400 font-semibold">
              Processing completed!
            </div>
          )}
          
          {processingStage === 'idle' && (
            <div className="text-gray-400">
              Click to start recording
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="text-xs text-gray-500 text-center">
          Speak clearly into your microphone. Audio will be saved as MP3 for testing.
        </div>
      </CardContent>
    </Card>
  );
};

export default AudioRecorder;
